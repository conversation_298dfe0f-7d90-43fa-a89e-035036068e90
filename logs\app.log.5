2025-06-03 23:37:54,316 INFO: 通过ID获取完整菜单对象成功: id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:39:00,787 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 23:39:03,094 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:39:15,195 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:39:15,196 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:39:15,196 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:39:15,196 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:39:15,200 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:39:15,201 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:39:15,202 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:39:15,202 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:39:15,202 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:39:15,203 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:39:15,203 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:39:15,204 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:39:15,204 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:39:15,204 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:39:15,205 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:39:15,205 INFO: SQL参数: area_id=42, week_start_str=2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:39:15,205 INFO: SQL查询成功，找到菜单: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:39:15,206 INFO: 通过ID获取完整菜单对象成功: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:39:15,207 INFO: 获取周菜单: area_id=42, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:39:15,207 INFO: 使用日期字符串: 2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:39:15,207 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:39:15,208 INFO: SQL参数: area_id=42, week_start_str=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:39:15,209 INFO: SQL查询成功，找到菜单: id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:39:15,209 INFO: 通过ID获取完整菜单对象成功: id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:42:14,580 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:42:14,580 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:42:14,581 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:42:14,581 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:42:14,584 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:42:14,585 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:42:14,586 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:42:14,586 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:42:14,587 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:42:14,593 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:42:14,636 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:42:14,636 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:42:14,637 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:42:14,637 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:42:14,637 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:42:14,638 INFO: SQL参数: area_id=42, week_start_str=2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:42:14,639 INFO: SQL查询成功，找到菜单: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:42:14,640 INFO: 通过ID获取完整菜单对象成功: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]

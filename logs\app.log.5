2025-06-03 18:41:27,663 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'storage_location_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount,
            MIN(so.stock_out_date) as first_out_date,
            MAX(so.stock_out_date) as last_out_date
        FROM stock_outs so
        JOIN stock_out_items soi ON so.id = soi.stock_out_id
        JOIN warehouses w ON so.warehouse_id = w.id
        JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND soi.storage_location_id = ? AND i.category_id = ?
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC
    ]
[parameters: ('2025-05-04', '2025-06-03', 9, 1)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 18:41:27,664 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'storage_location_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount,
            MIN(so.stock_out_date) as first_out_date,
            MAX(so.stock_out_date) as last_out_date
        FROM stock_outs so
        JOIN stock_out_items soi ON so.id = soi.stock_out_id
        JOIN warehouses w ON so.warehouse_id = w.id
        JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND soi.storage_location_id = ? AND i.category_id = ?
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC
    ]
[parameters: ('2025-05-27', '2025-06-03', 9, 1)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 18:41:27,840 ERROR: 库存统计查询失败: 'str' object has no attribute 'strftime' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 18:41:27,980 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'storage_location_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount,
            MIN(so.stock_out_date) as first_out_date,
            MAX(so.stock_out_date) as last_out_date
        FROM stock_outs so
        JOIN stock_out_items soi ON so.id = soi.stock_out_id
        JOIN warehouses w ON so.warehouse_id = w.id
        JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND soi.storage_location_id = ? AND i.category_id = ?
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC
    ]
[parameters: ('2025-05-04', '2025-06-03', 9, 1)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 18:42:06,141 ERROR: 库存统计打印失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'storage_location_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount,
            MIN(so.stock_out_date) as first_out_date,
            MAX(so.stock_out_date) as last_out_date
        FROM stock_outs so
        JOIN stock_out_items soi ON so.id = soi.stock_out_id
        JOIN warehouses w ON so.warehouse_id = w.id
        JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND soi.storage_location_id = ? AND i.category_id = ?
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC
    ]
[parameters: ('2025-05-04', '2025-06-03', 9, 1)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1334]
2025-06-03 18:49:49,526 INFO: 查询菜谱：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=午餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-03 18:49:49,540 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-03 18:49:49,616 INFO: 匹配条件的食谱有 7 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-03 18:49:49,736 INFO:   - 食谱: 米饭（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 18:49:49,830 INFO:   - 食谱: 鲜笋烧仔排（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 18:49:49,843 INFO:   - 食谱: 黑木耳炒山药（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 18:49:49,855 INFO:   - 食谱: 缤纷吐司蒸（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 18:49:49,923 INFO:   - 食谱: 鲜蚕豆烧大雁（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 18:49:49,933 INFO:   - 食谱: 西瓜桃子面（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 18:49:49,946 INFO:   - 食谱: 黄米南瓜盅（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 18:49:50,110 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=14, 多余=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:553]
2025-06-03 19:02:32,968 WARNING: Suspicious path blocked: /.env from 138.68.19.192 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 19:02:32,968 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 19:02:33,257 WARNING: Suspicious path blocked: /.git/config from 138.68.19.192 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 19:02:33,257 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 20:10:35,376 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 20:10:39,467 INFO: 查询菜谱：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=午餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-03 20:10:39,471 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-03 20:10:39,475 INFO: 匹配条件的食谱有 7 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-03 20:10:39,493 INFO:   - 食谱: 米饭（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 20:10:39,499 INFO:   - 食谱: 鲜笋烧仔排（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 20:10:39,505 INFO:   - 食谱: 黑木耳炒山药（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 20:10:39,509 INFO:   - 食谱: 缤纷吐司蒸（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 20:28:29,187 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]

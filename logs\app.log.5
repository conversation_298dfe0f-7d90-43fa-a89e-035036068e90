2025-06-03 22:59:50,837 ERROR: 库存统计查询执行失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(TRY_CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount
        FROM stock_outs so WITH (NOLOCK)
        INNER JOIN stock_out_items soi WITH (NOLOCK) ON so.id = soi.stock_out_id
        INNER JOIN warehouses w WITH (NOLOCK) ON so.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND i.category_id = ? AND soi.supplier_id = ? AND so.status = '已出库'
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY
    ]
[parameters: ('2025-05-04', '2025-06-03', 1, 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1154]
2025-06-03 22:59:54,422 ERROR: 库存统计查询执行失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(TRY_CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount
        FROM stock_outs so WITH (NOLOCK)
        INNER JOIN stock_out_items soi WITH (NOLOCK) ON so.id = soi.stock_out_id
        INNER JOIN warehouses w WITH (NOLOCK) ON so.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND i.category_id = ? AND soi.supplier_id = ? AND so.status = '已出库'
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY
    ]
[parameters: ('2025-05-04', '2025-06-03', 1, 28)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1154]
2025-06-03 23:00:37,943 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            ISNULL(ic.name, '其他') as category_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            COUNT(DISTINCT i.id) as ingredient_count,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity
        FROM stock_outs so
        JOIN stock_out_items soi ON so.id = soi.stock_out_id
        JOIN warehouses w ON so.warehouse_id = w.id
        JOIN ingredients i ON soi.ingredient_id = i.id
        LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND i.category_id = ? AND soi.supplier_id = ?
        GROUP BY ic.name
        ORDER BY total_out_quantity DESC
    ]
[parameters: ('2025-05-04', '2025-06-03', 9, 28)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1011]
2025-06-03 23:01:08,350 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 23:01:35,227 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:01:52,913 ERROR: 库存统计查询失败: Could not locate column in row for column 'first_in_date' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1011]
2025-06-03 23:01:59,895 ERROR: 库存统计查询失败: Could not locate column in row for column 'first_in_date' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1011]
2025-06-03 23:02:27,399 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:07:59,886 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 23:08:01,812 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:08:51,754 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:08:57,060 ERROR: 库存统计查询执行失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(TRY_CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount
        FROM stock_outs so WITH (NOLOCK)
        INNER JOIN stock_out_items soi WITH (NOLOCK) ON so.id = soi.stock_out_id
        INNER JOIN warehouses w WITH (NOLOCK) ON so.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND soi.supplier_id = ? AND so.status = '已出库'
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY
    ]
[parameters: ('2025-05-04', '2025-06-03', 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1154]
2025-06-03 23:10:13,437 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:10:57,037 ERROR: 库存统计打印失败: inventory/statistics_print.html [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1601]
2025-06-03 23:10:57,203 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:11:11,725 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:12:19,965 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:16:49,320 INFO: 查询菜谱：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=午餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-03 23:16:49,324 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-03 23:16:49,333 INFO: 匹配条件的食谱有 7 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-03 23:16:49,477 INFO:   - 食谱: 米饭（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 23:16:49,510 INFO:   - 食谱: 鲜笋烧仔排（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 23:16:49,603 INFO:   - 食谱: 黑木耳炒山药（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 23:16:49,717 INFO:   - 食谱: 缤纷吐司蒸（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 23:16:49,818 INFO:   - 食谱: 鲜蚕豆烧大雁（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 23:16:49,883 INFO:   - 食谱: 西瓜桃子面（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 23:16:49,950 INFO:   - 食谱: 黄米南瓜盅（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-03 23:16:49,977 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=14, 多余=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:553]
2025-06-03 23:19:00,580 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]

2025-06-03 23:20:57,315 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 23:23:18,129 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 23:23:21,969 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 23:24:51,554 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 23:25:16,577 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 23:26:59,598 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 23:28:28,728 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 23:28:28,849 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 23:37:10,835 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 23:37:11,035 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 23:37:17,623 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:37:17,623 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:37:17,623 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:37:17,624 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:37:17,627 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:37:17,629 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:37:17,630 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:37:17,630 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:37:17,630 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:37:17,630 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:37:17,631 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:37:17,632 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:37:17,632 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:37:17,632 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:37:17,632 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:37:17,633 INFO: SQL参数: area_id=42, week_start_str=2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:37:17,633 INFO: SQL查询成功，找到菜单: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:37:17,634 INFO: 通过ID获取完整菜单对象成功: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:37:17,635 INFO: 获取周菜单: area_id=42, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:37:17,635 INFO: 使用日期字符串: 2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:37:17,635 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:37:17,636 INFO: SQL参数: area_id=42, week_start_str=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:37:17,636 INFO: SQL查询成功，找到菜单: id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:37:17,637 INFO: 通过ID获取完整菜单对象成功: id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:37:22,404 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:37:22,404 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:37:22,405 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:37:22,405 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:37:22,407 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:37:22,409 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:37:22,409 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:37:22,409 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 23:37:22,410 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 23:37:22,410 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 23:37:22,411 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 23:37:22,411 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 23:37:22,412 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 23:37:22,412 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]

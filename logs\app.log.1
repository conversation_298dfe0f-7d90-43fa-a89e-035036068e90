2025-06-04 00:12:11,726 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,736 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,736 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,737 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,737 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 鲍汁茶树菇 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 鲍汁茶树菇, ID=372 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 鲜桃仁木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 鲜桃仁木耳炒山药, ID=373 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,739 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 韭菜炒软饼元 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,739 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 韭菜炒软饼元, ID=374 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,739 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,739 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,740 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,740 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,740 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,741 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,741 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,741 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,744 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 鲜桃仁木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,744 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 鲜桃仁木耳炒山药, ID=373 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,744 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 韭菜炒软饼元 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,744 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 韭菜炒软饼元, ID=374 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,745 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,745 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=375 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,745 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,745 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,746 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,746 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,746 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,746 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,747 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 青菜土豆泥 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,747 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 青菜土豆泥, ID=376 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,747 INFO: 主表数据补全完成，准备保存: 总菜品数=63, 已补全=63, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:369]
2025-06-04 00:12:11,753 INFO: 删除现有菜单食谱(主表): weekly_menu_id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:378]
2025-06-04 00:12:11,756 INFO: 删除现有菜单食谱(副表): weekly_menu_id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:387]
2025-06-04 00:12:11,756 WARNING: 跳过无效日期: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:396]
2025-06-04 00:12:11,756 WARNING: 跳过无效日期: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:396]
2025-06-04 00:12:11,757 WARNING: 跳过无效日期: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:396]
2025-06-04 00:12:11,964 INFO: 保存周菜单成功(主表和副表): id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:450]
2025-06-04 00:12:11,965 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 00:12:39,007 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 00:12:39,193 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 00:32:59,798 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-04 00:41:23,852 WARNING: Suspicious path blocked: /wp-admin/setup-config.php from 172.71.144.124 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 00:41:23,853 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 00:42:28,028 WARNING: Suspicious path blocked: /admin/settings from 124.229.114.174 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]

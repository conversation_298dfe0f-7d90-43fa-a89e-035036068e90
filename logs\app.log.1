2025-06-04 00:12:11,702 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,711 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,711 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 鲍汁茶树菇, ID=372 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,712 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,713 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,713 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 韭菜炒软饼元, ID=374 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,713 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,714 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲜桃仁木耳炒山药, ID=373 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,714 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲍汁茶树菇, ID=372 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,714 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,714 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,714 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=375 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,715 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,715 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,715 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 鲜桃仁木耳炒山药, ID=373 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,715 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,716 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 韭菜炒软饼元, ID=374 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,716 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,716 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,716 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,717 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,717 INFO: 副表数据: 日期=3, 餐次=早餐, 菜品=🏫 青菜土豆泥, ID=376 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,717 INFO: 副表数据: 日期=3, 餐次=午餐, 菜品=🏫 青菜土豆泥, ID=376 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,717 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 青菜土豆泥, ID=376 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:318]
2025-06-04 00:12:11,721 INFO: 副表数据映射构建完成: 3 天, 28 个菜品 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:320]
2025-06-04 00:12:11,721 WARNING: 跳过无效日期: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:330]
2025-06-04 00:12:11,721 WARNING: 跳过无效日期: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:330]
2025-06-04 00:12:11,721 WARNING: 跳过无效日期: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:330]
2025-06-04 00:12:11,722 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,722 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,722 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,722 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,723 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,723 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,723 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,723 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,724 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,724 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,724 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,724 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,725 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 鲍汁茶树菇 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,725 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 鲍汁茶树菇, ID=372 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,725 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 韭菜炒藕丝 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,726 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 韭菜炒藕丝, ID=375 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,726 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]
2025-06-04 00:12:11,726 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:351]
2025-06-04 00:12:11,726 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:339]

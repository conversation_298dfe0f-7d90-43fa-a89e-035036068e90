-- 简单的索引检查脚本
PRINT '检查食品留样管理相关表的索引...'
PRINT '========================================'

-- 1. 检查 food_samples 表的索引数量
PRINT '1. food_samples 表的索引：'
SELECT 
    i.name AS index_name,
    i.type_desc AS index_type,
    i.is_unique
FROM sys.indexes i
WHERE i.object_id = OBJECT_ID('food_samples')
    AND i.name IS NOT NULL
ORDER BY i.name

PRINT ''
PRINT '2. 检查索引总数：'
SELECT 
    OBJECT_NAME(i.object_id) AS table_name,
    COUNT(*) AS index_count
FROM sys.indexes i
WHERE i.object_id IN (
    OBJECT_ID('food_samples'), 
    OBJECT_ID('menu_plans'), 
    OBJECT_ID('recipes'), 
    OBJECT_ID('users'), 
    OBJECT_ID('administrative_areas')
)
    AND i.name IS NOT NULL
GROUP BY OBJECT_NAME(i.object_id)
ORDER BY index_count DESC

PRINT ''
PRINT '3. 查找我们创建的索引（包含safe/fixed/final后缀）：'
SELECT 
    OBJECT_NAME(i.object_id) AS table_name,
    i.name AS index_name
FROM sys.indexes i
WHERE i.object_id IN (
    OBJECT_ID('food_samples'), 
    OBJECT_ID('menu_plans'), 
    OBJECT_ID('recipes'), 
    OBJECT_ID('users'), 
    OBJECT_ID('administrative_areas')
)
    AND (i.name LIKE '%safe%' OR i.name LIKE '%fixed%' OR i.name LIKE '%final%')
ORDER BY table_name, index_name

PRINT ''
PRINT '========================================'
PRINT '检查完成！'
PRINT ''
PRINT '如果索引过多（food_samples表超过8个索引），建议清理。'
PRINT '如果查询仍然很慢，可能需要：'
PRINT '1. 删除多余的索引'
PRINT '2. 重建统计信息'
PRINT '3. 检查查询计划'
PRINT '========================================'

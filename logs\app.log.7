2025-06-03 20:39:42,269 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 20:42:11,093 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 20:46:48,017 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 20:47:19,696 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 20:47:23,576 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 20:48:02,725 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 20:48:07,670 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 20:50:37,277 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:05:04,944 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 21:05:49,563 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(so.stock_out_date AS DATE) as date,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            0 as stock_out_amount,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as stock_out_quantity
        FROM stock_outs so
        INNER JOIN stock_out_items soi ON so.id = soi.stock_out_id
        INNER JOIN warehouses w ON so.warehouse_id = w.id
        INNER JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND i.category_id = ? AND soi.supplier_id = ?
        GROUP BY CAST(so.stock_out_date AS DATE)
        ORDER BY CAST(so.stock_out_date AS DATE)
    ]
[parameters: ('2025-04-30', '2025-05-30', 1, 28)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1014]
2025-06-03 21:05:52,840 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 21:05:55,412 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 21:05:58,842 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 21:11:56,093 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 21:12:04,253 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(so.stock_out_date AS DATE) as date,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            0 as stock_out_amount,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as stock_out_quantity
        FROM stock_outs so
        INNER JOIN stock_out_items soi ON so.id = soi.stock_out_id
        INNER JOIN warehouses w ON so.warehouse_id = w.id
        INNER JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND i.category_id = ? AND soi.supplier_id = ?
        GROUP BY CAST(so.stock_out_date AS DATE)
        ORDER BY CAST(so.stock_out_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03', 5, 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1014]
2025-06-03 21:16:39,150 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(so.stock_out_date AS DATE) as date,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            0 as stock_out_amount,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as stock_out_quantity
        FROM stock_outs so
        INNER JOIN stock_out_items soi ON so.id = soi.stock_out_id
        INNER JOIN warehouses w ON so.warehouse_id = w.id
        INNER JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND i.category_id = ? AND soi.supplier_id = ?
        GROUP BY CAST(so.stock_out_date AS DATE)
        ORDER BY CAST(so.stock_out_date AS DATE)
    ]
[parameters: ('2025-05-27', '2025-06-03', 5, 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1014]
2025-06-03 21:17:10,105 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:18:07,246 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:18:59,896 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:19:56,822 WARNING: Suspicious path blocked: /.c9/metadata/environment/.env from 196.251.85.66 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 21:19:56,822 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 21:19:59,645 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:21:06,960 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:22:00,721 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 21:22:38,437 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:23:49,980 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:24:23,109 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 21:25:52,863 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:30:35,916 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:31:34,796 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:32:47,427 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:33:47,691 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:56:27,539 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 21:59:06,405 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 22:00:17,400 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 22:01:29,548 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 22:02:29,167 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 22:04:00,472 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 22:04:03,689 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:967]
2025-06-03 22:10:25,537 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 22:10:30,025 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:10:36,841 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]

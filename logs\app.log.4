2025-06-03 20:28:32,831 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:953]
2025-06-03 20:29:20,222 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:953]
2025-06-03 20:30:03,747 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:953]
2025-06-03 20:30:13,706 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]表 'stock_ins' 的索引 'IX_stock_ins_date_warehouse' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as stock_in_amount,
            SUM(CAST(sii.quantity AS DECIMAL(18,2))) as stock_in_quantity
        FROM stock_ins si WITH (INDEX(IX_stock_ins_date_warehouse))
        INNER JOIN stock_in_items sii WITH (INDEX(IX_stock_in_items_stock_in_id)) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w ON si.warehouse_id = w.id
        INNER JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ? AND sii.supplier_id = ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03', 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:999]
2025-06-03 20:30:23,501 WARNING: Suspicious path blocked: /xmlrpc.php from 81.88.53.43 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 20:30:23,502 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 20:31:27,214 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]表 'stock_ins' 的索引 'IX_stock_ins_date_warehouse' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as stock_in_amount,
            SUM(CAST(sii.quantity AS DECIMAL(18,2))) as stock_in_quantity
        FROM stock_ins si WITH (INDEX(IX_stock_ins_date_warehouse))
        INNER JOIN stock_in_items sii WITH (INDEX(IX_stock_in_items_stock_in_id)) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w ON si.warehouse_id = w.id
        INNER JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:999]
2025-06-03 20:35:40,685 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:953]
2025-06-03 20:35:45,414 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]表 'stock_ins' 的索引 'IX_stock_ins_date_warehouse' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as stock_in_amount,
            SUM(CAST(sii.quantity AS DECIMAL(18,2))) as stock_in_quantity
        FROM stock_ins si WITH (INDEX(IX_stock_ins_date_warehouse))
        INNER JOIN stock_in_items sii WITH (INDEX(IX_stock_in_items_stock_in_id)) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w ON si.warehouse_id = w.id
        INNER JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ? AND sii.supplier_id = ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03', 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:999]
2025-06-03 20:35:53,157 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]表 'stock_ins' 的索引 'IX_stock_ins_date_warehouse' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as stock_in_amount,
            SUM(CAST(sii.quantity AS DECIMAL(18,2))) as stock_in_quantity
        FROM stock_ins si WITH (INDEX(IX_stock_ins_date_warehouse))
        INNER JOIN stock_in_items sii WITH (INDEX(IX_stock_in_items_stock_in_id)) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w ON si.warehouse_id = w.id
        INNER JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ? AND i.category_id = ? AND sii.supplier_id = ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03', 1, 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:999]
2025-06-03 20:36:02,191 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]表 'stock_ins' 的索引 'IX_stock_ins_date_warehouse' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as stock_in_amount,
            SUM(CAST(sii.quantity AS DECIMAL(18,2))) as stock_in_quantity
        FROM stock_ins si WITH (INDEX(IX_stock_ins_date_warehouse))
        INNER JOIN stock_in_items sii WITH (INDEX(IX_stock_in_items_stock_in_id)) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w ON si.warehouse_id = w.id
        INNER JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ? AND i.category_id = ? AND sii.supplier_id = ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-27', '2025-06-03', 1, 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:999]
2025-06-03 20:36:11,385 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]表 'stock_ins' 的索引 'IX_stock_ins_date_warehouse' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as stock_in_amount,
            SUM(CAST(sii.quantity AS DECIMAL(18,2))) as stock_in_quantity
        FROM stock_ins si WITH (INDEX(IX_stock_ins_date_warehouse))
        INNER JOIN stock_in_items sii WITH (INDEX(IX_stock_in_items_stock_in_id)) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w ON si.warehouse_id = w.id
        INNER JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ? AND i.category_id = ? AND sii.supplier_id = ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03', 1, 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:999]

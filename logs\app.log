2025-06-03 22:11:13,943 ERROR: 库存统计打印失败: inventory/statistics_print.html [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1397]
2025-06-03 22:11:14,136 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:17:28,754 INFO: 更新了日期 2025-05-26 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:28,763 INFO: 更新了日期 2025-05-27 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:28,980 INFO: 更新了日期 2025-05-28 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,055 INFO: 更新了日期 2025-05-29 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,321 INFO: 更新了日期 2025-05-30 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,407 INFO: 更新了日期 2025-05-31 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,501 INFO: 更新了日期 2025-06-01 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,638 INFO: 更新了日期 2025-06-02 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,665 INFO: 更新了日期 2025-06-03 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,755 INFO: 更新了日期 2025-06-04 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,767 INFO: 更新了日期 2025-06-05 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:29,937 INFO: 更新了日期 2025-06-06 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:30,013 INFO: 更新了日期 2025-06-07 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:17:30,039 INFO: 更新了日期 2025-06-08 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-03 22:21:12,734 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 22:21:15,709 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:30:11,182 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:31:40,266 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:32:18,234 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:33:06,152 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:33:06,256 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount,
            MIN(so.stock_out_date) as first_out_date,
            MAX(so.stock_out_date) as last_out_date
        FROM stock_outs so
        JOIN stock_out_items soi ON so.id = soi.stock_out_id
        JOIN warehouses w ON so.warehouse_id = w.id
        JOIN ingredients i ON soi.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(so.stock_out_date AS DATE) >= ? AND CAST(so.stock_out_date AS DATE) <= ? AND i.category_id = ? AND soi.supplier_id = ?
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC
    ]
[parameters: ('2025-05-04', '2025-06-03', 1, 28)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:1011]
2025-06-03 22:34:00,855 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:34:05,240 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:37:31,417 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:39:34,819 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 22:39:37,110 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:40:05,917 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:40:51,267 INFO: 库存统计页面：为区域 [42] 找到 2 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-03 22:40:52,026 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 22:40:57,481 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
